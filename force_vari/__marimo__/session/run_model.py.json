{"version": "1", "metadata": {"marimo_version": "0.17.8"}, "cells": [{"id": "lEQa", "code_hash": "7c904eb87fcc511d4df6050c0d346368", "outputs": [{"type": "data", "data": {"text/markdown": "<span class=\"markdown prose dark:prose-invert contents\"><h2 id=\"model-1\">Model 1</h2></span>"}}], "console": []}, {"id": "<PERSON><PERSON><PERSON>", "code_hash": "b85ea5297a8d85270b5125134354cc70", "outputs": [{"type": "data", "data": {"text/markdown": "<span class=\"markdown prose dark:prose-invert contents\"><h2 id=\"better-model\">better model</h2></span>"}}], "console": []}, {"id": "Hbol", "code_hash": "1d0db38904205bec4d6f6f6a1f6cec3e", "outputs": [], "console": []}, {"id": "MJUe", "code_hash": "5a704016d93bd0ca05f45404a9b5991a", "outputs": [], "console": []}, {"id": "vblA", "code_hash": "3d896196fc3b168afd40d869ca276e9a", "outputs": [], "console": []}, {"id": "bkHC", "code_hash": "a28b35b3b400b89eb22e3e9667c4734f", "outputs": [], "console": []}, {"id": "P<PERSON><PERSON>", "code_hash": "c6e01f1cf0484bcbfbc6dcbff0a74797", "outputs": [], "console": []}, {"id": "BYtC", "code_hash": "65803ff2f96af8f6d3ac90b0018e56fa", "outputs": [], "console": []}, {"id": "RGSE", "code_hash": "98e94343c9dc3e504b82cb8f48a2a328", "outputs": [], "console": []}]}