{"version": "1", "metadata": {"marimo_version": "0.17.0"}, "cells": [{"id": "Hbol", "code_hash": null, "outputs": [], "console": []}, {"id": "MJUe", "code_hash": "b6bd3482c5f1f91b8f7b7d123cbf3e2b", "outputs": [{"type": "data", "data": {"text/plain": ""}}], "console": []}, {"id": "vblA", "code_hash": "9d76d3a288e11bd19b1b0eab50ef3640", "outputs": [{"type": "data", "data": {"text/plain": ""}}], "console": []}, {"id": "bkHC", "code_hash": "7f7a155641a2a549b25436af11c8be37", "outputs": [{"type": "data", "data": {"text/plain": ""}}], "console": []}, {"id": "lEQa", "code_hash": "640e98aaa99e987fedc7428f8c67416b", "outputs": [{"type": "data", "data": {"text/plain": ""}}], "console": []}, {"id": "P<PERSON><PERSON>", "code_hash": "c12296cb8aaf3eff25c1a49e59f126a2", "outputs": [], "console": []}, {"id": "<PERSON><PERSON><PERSON>", "code_hash": "2f44105fccd8f155d1e181f448fc22ce", "outputs": [], "console": []}, {"id": "SFPL", "code_hash": "2ae4c4ae6b0b51472e25ca27f77c1d2c", "outputs": [], "console": []}, {"id": "BYtC", "code_hash": "36c2baedf82b2b101ce434cfb682b51e", "outputs": [], "console": []}, {"id": "RGSE", "code_hash": "b53c7f6fc6f1da004e791d9b5cf122cd", "outputs": [], "console": []}, {"id": "Kclp", "code_hash": "f97bb940fa256abee2f0f28b011db2a3", "outputs": [], "console": []}, {"id": "emfo", "code_hash": "69ee50eea4f91b3828140bde04d8865e", "outputs": [], "console": []}, {"id": "Hstk", "code_hash": "0fa519a29be1703136da814e6165d937", "outputs": [], "console": []}, {"id": "nWHF", "code_hash": "c1b67c9a650f0ffd1fb09cfcf7263ca6", "outputs": [], "console": []}, {"id": "iLit", "code_hash": "a88d0a207f549e947530ac9d1f65b972", "outputs": [], "console": []}, {"id": "ZHCJ", "code_hash": "ed78a75ea13159c2f2416168cf0dc6e6", "outputs": [], "console": []}, {"id": "ROlb", "code_hash": null, "outputs": [], "console": []}, {"id": "qnkX", "code_hash": null, "outputs": [], "console": []}]}